"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { exportDeviceTrackingReport, exportDeviceTrackingReportDirect, printElement, printDeviceData } from "@/lib/export-utils/html-to-pdf";
import { createArabicPDFWithCanvas } from "@/lib/export-utils/canvas-pdf";
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export default function TestExportPage() {
  const [deviceId, setDeviceId] = useState("123456789012345");
  
  // بيانات تجريبية لاختبار التصدير
  const sampleDeviceData = {
    model: "iPhone 14 Pro Max",
    id: deviceId,
    status: "متاح للبيع",
    lastSale: {
      clientName: "أحمد محمد علي",
      soNumber: "SO-2024-001",
      opNumber: "OP-2024-001",
      date: new Date()
    },
    warrantyInfo: {
      status: "ساري المفعول",
      expiryDate: "2025-12-31",
      remaining: "11 شهر و 15 يوم"
    },
    originalItemInfo: null
  };

  const sampleTimelineEvents = [
    {
      date: new Date(),
      formattedDate: new Date(),
      title: "استلام الجهاز",
      description: "تم استلام الجهاز من المورد",
      user: "محمد أحمد"
    },
    {
      date: new Date(Date.now() - 86400000),
      formattedDate: new Date(Date.now() - 86400000),
      title: "فحص الجهاز",
      description: "تم فحص الجهاز والتأكد من سلامته",
      user: "سارة محمود"
    },
    {
      date: new Date(Date.now() - 172800000),
      formattedDate: new Date(Date.now() - 172800000),
      title: "إضافة إلى المخزن",
      description: "تم إضافة الجهاز إلى مخزن الفرع الرئيسي",
      user: "علي حسن"
    },
    {
      date: new Date(Date.now() - 259200000),
      formattedDate: new Date(Date.now() - 259200000),
      title: "بيع الجهاز",
      description: "تم بيع الجهاز للعميل أحمد محمد علي",
      user: "فاطمة أحمد"
    }
  ];

  const handleTestExport = (isCustomerView: boolean, method: 'direct' | 'print' | 'canvas' | 'print-direct' = 'direct') => {
    const fileName = `test_device_report_${deviceId}_${isCustomerView ? 'customer' : 'full'}`;

    if (method === 'canvas') {
      // الطريقة الجديدة (Canvas - حل مشكلة الأحرف العربية)
      createArabicPDFWithCanvas(
        sampleDeviceData,
        sampleTimelineEvents,
        fileName,
        isCustomerView
      );
    } else if (method === 'print-direct') {
      // الطباعة المباشرة المحسنة - استخدام البيانات مباشرة
      const title = isCustomerView ? 'تقرير تتبع الجهاز (نسخة العميل)' : 'سجل تاريخ الجهاز الكامل';
      printDeviceData(sampleDeviceData, sampleTimelineEvents, title, isCustomerView);
    } else if (method === 'direct') {
      // الطريقة المحسنة (مباشرة بدون نافذة)
      exportDeviceTrackingReportDirect(
        sampleDeviceData,
        sampleTimelineEvents,
        fileName,
        isCustomerView
      );
    } else {
      // الطريقة القديمة (عبر نافذة الطباعة)
      exportDeviceTrackingReport(
        sampleDeviceData,
        sampleTimelineEvents,
        fileName,
        isCustomerView
      );
    }
  };

  return (
    <div className="container mx-auto p-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle>اختبار تصدير PDF للعربية</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="deviceId">الرقم التسلسلي للجهاز</Label>
            <Input
              id="deviceId"
              value={deviceId}
              onChange={(e) => setDeviceId(e.target.value)}
              placeholder="أدخل الرقم التسلسلي"
            />
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">البيانات التجريبية:</h3>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <p><strong>الموديل:</strong> {sampleDeviceData.model}</p>
              <p><strong>الرقم التسلسلي:</strong> {sampleDeviceData.id}</p>
              <p><strong>الحالة:</strong> {sampleDeviceData.status}</p>
              <p><strong>العميل:</strong> {sampleDeviceData.lastSale?.clientName}</p>
              <p><strong>عدد الأحداث:</strong> {sampleTimelineEvents.length}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-green-50 p-3 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-800 mb-2">✅ الطريقة المُوصى بها (Canvas)</h4>
              <div className="flex gap-4">
                <Button
                  onClick={() => handleTestExport(false, 'canvas')}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  تصدير التقرير الكامل (Canvas)
                </Button>
                <Button
                  onClick={() => handleTestExport(true, 'canvas')}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  تصدير نسخة العميل (Canvas)
                </Button>
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">🖨️ الطباعة المباشرة</h4>
              <div className="flex gap-4">
                <Button
                  onClick={() => handleTestExport(false, 'print-direct')}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  طباعة التقرير الكامل
                </Button>
                <Button
                  onClick={() => handleTestExport(true, 'print-direct')}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  طباعة نسخة العميل
                </Button>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                onClick={() => handleTestExport(false, 'direct')}
                variant="outline"
                className="flex-1"
              >
                تصدير التقرير الكامل (HTML)
              </Button>
              <Button
                onClick={() => handleTestExport(true, 'direct')}
                variant="outline"
                className="flex-1"
              >
                تصدير نسخة العميل (HTML)
              </Button>
            </div>

            <div className="flex gap-4">
              <Button
                onClick={() => handleTestExport(false, 'print')}
                variant="secondary"
                className="flex-1"
              >
                تصدير التقرير الكامل (طباعة)
              </Button>
              <Button
                onClick={() => handleTestExport(true, 'print')}
                variant="secondary"
                className="flex-1"
              >
                تصدير نسخة العميل (طباعة)
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ملاحظات الاختبار:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Canvas:</strong> يرسم النصوص العربية كصور - يحل مشكلة الأحرف الغريبة نهائياً</li>
              <li>• <strong>الطباعة المباشرة:</strong> تفتح نافذة طباعة محسنة مع خطوط عربية واضحة</li>
              <li>• <strong>HTML:</strong> يستخدم HTML مع خط عربي محسن (Cairo, Noto Sans Arabic)</li>
              <li>• <strong>الطباعة:</strong> يفتح نافذة طباعة يمكن حفظها كـ PDF</li>
              <li>• التقرير الكامل يحتوي على جميع الأحداث والتفاصيل</li>
              <li>• نسخة العميل تحتوي على معلومات البيع والضمان فقط</li>
              <li>• طريقة Canvas مُوصى بها لحل مشكلة ترميز الأحرف العربية</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* عنصر مخفي للطباعة المباشرة */}
      <div id="test-report-container" className="hidden print:block">
        <div className="space-y-6">
          <div className="text-center border-b-2 border-blue-500 pb-4 mb-6">
            <h1 className="text-2xl font-bold text-gray-800">تقرير تتبع الجهاز (اختبار)</h1>
            <p className="text-sm text-gray-600 mt-2">
              تم إنشاؤه في: {new Date()} - {new Date().toLocaleTimeString('ar-EG')}
            </p>
          </div>

          <div className="card">
            <div className="card-header">
              <h2 className="card-title">معلومات الجهاز</h2>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-2 gap-4">
                <div><strong>الموديل:</strong> {sampleDeviceData.model}</div>
                <div><strong>الرقم التسلسلي:</strong> {sampleDeviceData.id}</div>
                <div><strong>الحالة:</strong> {sampleDeviceData.status}</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h2 className="card-title">تفاصيل البيع</h2>
            </div>
            <div className="card-content">
              <div className="grid grid-cols-2 gap-4">
                <div><strong>العميل:</strong> {sampleDeviceData.lastSale?.clientName}</div>
                <div><strong>فاتورة البيع:</strong> {sampleDeviceData.lastSale?.soNumber}</div>
                <div><strong>الفاتورة الرسمية:</strong> {sampleDeviceData.lastSale?.opNumber}</div>
                <div><strong>تاريخ البيع:</strong> {new Date()}</div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-header">
              <h2 className="card-title">سجل الأحداث</h2>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {sampleTimelineEvents.map((event, index) => (
                  <div key={index} className="timeline-event">
                    <div className="timeline-title">{event.title}</div>
                    <div className="text-sm text-gray-600 mb-2">{event.description}</div>
                    <div className="timeline-date">
                      {event.formattedDate} - {event.user}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
