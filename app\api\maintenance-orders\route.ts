import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع أوامر الصيانة مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { id: 'asc' as const }
      : { id: 'desc' as const };

    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      orderBy
    });

    return NextResponse.json(maintenanceOrders);
  } catch (error) {
    console.error('Failed to fetch maintenance orders:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.employeeName && !authResult.user!.username) {
      return NextResponse.json(
        { error: 'Employee name is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      let orderNumber = newOrder.orderNumber;

      // إذا لم يتم توفير رقم أمر أو كان الرقم المرسل موجوداً بالفعل، نولد رقماً جديداً
      if (!orderNumber) {
        // تحديد نمط الترقيم بناءً على مصدر الأمر
        const orderPrefix = newOrder.source === 'warehouse' ? 'MTRANS-' : 'MAINT-';
        
        // إنشاء رقم تسلسلي
        const existingOrders = await tx.maintenanceOrder.findMany({
          select: { orderNumber: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;
        existingOrders.forEach(order => {
          const match = order.orderNumber.match(new RegExp(`${orderPrefix.replace('-', '-')}(\\d+)$`));
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        orderNumber = `${orderPrefix}${maxNumber + 1}`;
      } else {
        // التحقق من وجود الرقم المرسل
        const existingOrder = await tx.maintenanceOrder.findUnique({
          where: { orderNumber }
        });

        if (existingOrder) {
          // إذا كان الرقم موجوداً، نولد رقماً جديداً بدلاً من إرجاع خطأ
          const orderPrefix = newOrder.source === 'warehouse' ? 'MTRANS-' : 'MAINT-';
          
          const existingOrders = await tx.maintenanceOrder.findMany({
            select: { orderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxNumber = 0;
          existingOrders.forEach(order => {
            const match = order.orderNumber.match(new RegExp(`${orderPrefix.replace('-', '-')}(\\d+)$`));
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          });

          orderNumber = `${orderPrefix}${maxNumber + 1}`;
        }
      }

      // Create the maintenance order in the database
      const order = await tx.maintenanceOrder.create({
        data: {
          orderNumber,
          referenceNumber: newOrder.referenceNumber || null,
          date: newOrder.date || new Date(),
          employeeName: newOrder.employeeName || authResult.user!.username,
          maintenanceEmployeeName: newOrder.maintenanceEmployeeName || null,
          items: newOrder.items ? JSON.stringify(newOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),
          notes: newOrder.notes || null,
          status: newOrder.status || 'wip',
          source: newOrder.source || 'warehouse',
          attachmentName: newOrder.attachmentName || null,
        }
      });

      // Update device statuses if items are provided
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          if (item.deviceId || item.id) {
            const deviceId = item.deviceId || item.id;
            const device = await tx.device.findUnique({
              where: { id: deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: deviceId },
                data: { status: 'بانتظار استلام في الصيانة' }
              });
            } else {
              console.warn(`Device ${deviceId} not found for maintenance order ${order.orderNumber}`);
            }
          }
        }
      }

      
      // Create maintenanceOrder items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.maintenanceOrderItem.create({
            data: {
              maintenanceOrderId: order.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              fault: item.fault || null,
              notes: item.notes || null
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created maintenance order: ${order.orderNumber}`,
        tableName: 'maintenanceOrder',
        recordId: order.id.toString()
      });

      // Return the created order (items are already included as JSON string)
      return order;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order with this number already exists') {
      return NextResponse.json({ error: 'Maintenance order with this number already exists' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create maintenance order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    if (!updatedOrder.id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance order exists
      const existingOrder = await tx.maintenanceOrder.findUnique({
        where: { id: updatedOrder.id }
      });

      if (!existingOrder) {
        throw new Error('Maintenance order not found');
      }

      // Update the maintenance order
      const order = await tx.maintenanceOrder.update({
        where: { id: updatedOrder.id },
        data: {
          orderNumber: updatedOrder.orderNumber || existingOrder.orderNumber,
          referenceNumber: updatedOrder.referenceNumber !== undefined ? updatedOrder.referenceNumber : existingOrder.referenceNumber,
          date: updatedOrder.date || existingOrder.date,
          employeeName: updatedOrder.employeeName || existingOrder.employeeName,
          maintenanceEmployeeName: updatedOrder.maintenanceEmployeeName !== undefined ? updatedOrder.maintenanceEmployeeName : existingOrder.maintenanceEmployeeName,
          items: updatedOrder.items ? JSON.stringify(updatedOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : existingOrder.items,
          notes: updatedOrder.notes !== undefined ? updatedOrder.notes : existingOrder.notes,
          status: updatedOrder.status || existingOrder.status,
          source: updatedOrder.source || existingOrder.source,
          attachmentName: updatedOrder.attachmentName !== undefined ? updatedOrder.attachmentName : existingOrder.attachmentName,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated maintenance order: ${order.orderNumber}`,
        tableName: 'maintenanceOrder',
        recordId: order.id.toString()
      });

      // Return order with items
      const orderWithItems = await tx.maintenanceOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order not found') {
      return NextResponse.json({ error: 'Maintenance order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance order exists
      const existingOrder = await tx.maintenanceOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingOrder) {
        throw new Error('Maintenance order not found');
      }

      // Parse items to update device statuses back
      let items = [];
      try {
        items = typeof existingOrder.items === 'string' ?
          existingOrder.items : existingOrder.items;
      } catch (error) {
        console.warn('Failed to parse items for device status update:', error);
      }

      // Update device statuses back to available
      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.deviceId || item.id) {
            const deviceId = item.deviceId || item.id;
            const device = await tx.device.findUnique({
              where: { id: deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: deviceId },
                data: { status: 'متاح للبيع' }
              });
            }
          }
        }
      }
      // Delete the maintenance order
      await tx.maintenanceOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted maintenance order: ${existingOrder.orderNumber}`,
        tableName: 'maintenanceOrder',
        recordId: id.toString()
      });

      return { message: 'Maintenance order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order not found') {
      return NextResponse.json({ error: 'Maintenance order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
