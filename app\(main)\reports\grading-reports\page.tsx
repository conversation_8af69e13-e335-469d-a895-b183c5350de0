
'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Printer, FileDown, Eye } from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings, Device, EvaluationGrade, ScreenGrade, NetworkGrade, FinalGrade, EvaluatedDevice } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { <PERSON>roll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';


const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc.setFontSize(16).text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(settings.reportFooter, 195, doc.internal.pageSize.height - 10, {
          align: 'right',
        });
      }
    };
    return { addHeader, addFooter };
  };

type ModelSummary = {
    model: string;
    total: number;
    grades: {
        final: Record<string, number>;
        external: Record<string, number>;
        screen: Record<string, number>;
        network: Record<string, number>;
    };
};

type DetailsDialogData = {
    model: string;
    devices: (Device & { evaluation: EvaluatedDevice })[];
}

export default function GradingReports() {
  const { devices, evaluationOrders, warehouses, systemSettings } = useStore();
  
  const [filters, setFilters] = useState({
    model: 'all',
    finalGrade: 'all' as FinalGrade | 'all',
    externalGrade: 'all' as EvaluationGrade | 'all',
    screenGrade: 'all' as ScreenGrade | 'all',
    networkGrade: 'all' as NetworkGrade | 'all',
  });
  
  const [detailsModalData, setDetailsModalData] = useState<DetailsDialogData | null>(null);

  const devicesWithEvals = useMemo(() => {
    return devices.map(device => {
      const relevantEvals = evaluationOrders
        .flatMap(order => (Array.isArray(order.items) ? order.items : []).filter(item => item.deviceId === device.id))
        .sort((a, b) => {
            const dateA = evaluationOrders.find(o => (Array.isArray(o.items) && o.items.some(i => i.deviceId === a.deviceId)))?.date || '';
            const dateB = evaluationOrders.find(o => (Array.isArray(o.items) && o.items.some(i => i.deviceId === b.deviceId)))?.date || '';
            return new Date(dateB).getTime() - new Date(dateA).getTime();
        });

      const latestEval = relevantEvals[0];

      return {
        ...device,
        evaluation: latestEval,
      };
    }).filter(d => d.evaluation);
  }, [devices, evaluationOrders]);

  const filteredDevicesWithEvals = useMemo(() => {
    return devicesWithEvals.filter(d => {
        if (filters.model !== 'all' && d.model !== filters.model) return false;
        if (filters.finalGrade !== 'all' && d.evaluation.finalGrade !== filters.finalGrade) return false;
        if (filters.externalGrade !== 'all' && d.evaluation.externalGrade !== filters.externalGrade) return false;
        if (filters.screenGrade !== 'all' && d.evaluation.screenGrade !== filters.screenGrade) return false;
        if (filters.networkGrade !== 'all' && d.evaluation.networkGrade !== filters.networkGrade) return false;
        return true;
      });
  }, [devicesWithEvals, filters]);


  const filteredSummary = useMemo((): ModelSummary[] => {
    const summary = filteredDevicesWithEvals.reduce((acc, device) => {
        if (!acc[device.model]) {
            acc[device.model] = {
                model: device.model,
                total: 0,
                grades: {
                    final: {},
                    external: {},
                    screen: {},
                    network: {},
                },
            };
        }
        const modelSummary = acc[device.model];
        modelSummary.total++;
        const evalData = device.evaluation;
        modelSummary.grades.final[evalData.finalGrade] = (modelSummary.grades.final[evalData.finalGrade] || 0) + 1;
        modelSummary.grades.external[evalData.externalGrade] = (modelSummary.grades.external[evalData.externalGrade] || 0) + 1;
        modelSummary.grades.screen[evalData.screenGrade] = (modelSummary.grades.screen[evalData.screenGrade] || 0) + 1;
        modelSummary.grades.network[evalData.networkGrade] = (modelSummary.grades.network[evalData.networkGrade] || 0) + 1;
        
        return acc;
    }, {} as Record<string, ModelSummary>);

    return Object.values(summary).sort((a, b) => b.total - a.total);

  }, [filteredDevicesWithEvals]);
  
  const modelOptions = [...new Set(devices.map((d) => d.model))];
  const finalGradeOptions: FinalGrade[] = ['جاهز للبيع', 'يحتاج صيانة', 'عيب فني', 'تالف'];
  const externalGradeOptions: EvaluationGrade[] = ['بدون', 'A++', 'A', 'B', 'C', 'D'];
  const screenGradeOptions: ScreenGrade[] = ['بدون', 'A+', 'M1', 'M2', 'M3', 'M4', 'N1', 'N2', 'F1', 'F2', 'F3'];
  const networkGradeOptions: NetworkGrade[] = ['مفتوح رسمي', 'مغلق'];

  const handleExport = (action: 'print' | 'download') => {
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();
    
    const title = 'تقرير الأجهزة حسب التقييم';
    doc.setFontSize(20).text(title, 190, 45, { align: 'right' });

    const head = [['الإجمالي', 'الشبكة', 'الشاشة', 'الخارجي', 'النهائي', 'الموديل']];
    const body = filteredSummary.map(item => [
        item.total,
        Object.entries(item.grades.network).map(([k,v]) => `${k}: ${v}`).join(', '),
        Object.entries(item.grades.screen).map(([k,v]) => `${k}: ${v}`).join(', '),
        Object.entries(item.grades.external).map(([k,v]) => `${k}: ${v}`).join(', '),
        Object.entries(item.grades.final).map(([k,v]) => `${k}: ${v}`).join(', '),
        item.model,
    ]);

    autoTable(doc, {
        startY: 55,
        head,
        body,
        theme: 'grid',
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        didDrawPage: addFooter
    });

    if (action === 'print') {
        doc.output('dataurlnewwindow');
    } else {
        doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف
    }
  };
  
  const handleViewDetails = (modelName: string) => {
    const devicesForModel = filteredDevicesWithEvals.filter(d => d.model === modelName);
    setDetailsModalData({ model: modelName, devices: devicesForModel });
  }

  return (
    <>
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>فلاتر تقرير التقييم</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div className="space-y-2">
            <Label>الموديل</Label>
            <Select dir="rtl" value={filters.model} onValueChange={(value) => setFilters(f => ({...f, model: value}))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموديلات</SelectItem>
                {modelOptions.sort().map((model) => (
                  <SelectItem key={model} value={model}>{model}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
           <div className="space-y-2">
            <Label>التقييم النهائي</Label>
            <Select dir="rtl" value={filters.finalGrade} onValueChange={(value) => setFilters(f => ({...f, finalGrade: value as any}))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل التقييمات</SelectItem>
                {finalGradeOptions.map((grade) => (
                  <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>التقييم الخارجي</Label>
            <Select dir="rtl" value={filters.externalGrade} onValueChange={(value) => setFilters(f => ({...f, externalGrade: value as any}))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">الكل</SelectItem>
                {externalGradeOptions.map((grade) => (
                  <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>تقييم الشاشة</Label>
            <Select dir="rtl" value={filters.screenGrade} onValueChange={(value) => setFilters(f => ({...f, screenGrade: value as any}))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">الكل</SelectItem>
                {screenGradeOptions.map((grade) => (
                  <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>تقييم الشبكة</Label>
            <Select dir="rtl" value={filters.networkGrade} onValueChange={(value) => setFilters(f => ({...f, networkGrade: value as any}))}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">الكل</SelectItem>
                {networkGradeOptions.map((grade) => (
                  <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>نتائج البحث ({filteredSummary.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الموديل</TableHead>
                  <TableHead>الإجمالي</TableHead>
                  <TableHead>التقييم النهائي</TableHead>
                  <TableHead>التقييم الخارجي</TableHead>
                  <TableHead>تقييم الشاشة</TableHead>
                  <TableHead>الشبكة</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSummary.length > 0 ? (
                  filteredSummary.map((item) => (
                    <TableRow key={item.model}>
                      <TableCell className="font-medium">{item.model}</TableCell>
                      <TableCell>
                        <Badge>{item.total}</Badge>
                      </TableCell>
                       <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(item.grades.final).map(([grade, count]) => (
                            <Badge key={grade} variant={grade === 'جاهز للبيع' ? 'default' : grade === 'تالف' || grade === 'عيب فني' ? 'destructive' : 'secondary'}>
                              {grade}: {count}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(item.grades.external).map(([grade, count]) => (
                            <Badge key={grade} variant="outline">{grade}: {count}</Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                         <div className="flex flex-wrap gap-1">
                          {Object.entries(item.grades.screen).map(([grade, count]) => (
                            <Badge key={grade} variant="outline">{grade}: {count}</Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(item.grades.network).map(([grade, count]) => (
                            <Badge key={grade} variant="outline">{grade}: {count}</Badge>
                          ))}
                        </div>
                      </TableCell>
                       <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetails(item.model)}>
                           <Eye className="ml-2 h-4 w-4"/> عرض
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      لا توجد أجهزة تطابق الفلاتر المحددة.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('print')} disabled={filteredSummary.length === 0}>
                <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
            <Button variant="outline" onClick={() => handleExport('download')} disabled={filteredSummary.length === 0}>
                <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
        </CardFooter>
      </Card>
    </div>
    
    <Dialog open={!!detailsModalData} onOpenChange={() => setDetailsModalData(null)}>
        <DialogContent className="sm:max-w-4xl">
            <DialogHeader>
                <DialogTitle>تفاصيل أجهزة موديل: {detailsModalData?.model}</DialogTitle>
            </DialogHeader>
            <ScrollArea className="max-h-[60vh] pr-4">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>الرقم التسلسلي</TableHead>
                        <TableHead>التقييم النهائي</TableHead>
                        <TableHead>التقييم الخارجي</TableHead>
                        <TableHead>تقييم الشاشة</TableHead>
                        <TableHead>الشبكة</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {detailsModalData?.devices.map(device => (
                        <TableRow key={device.id}>
                            <TableCell dir="ltr">{device.id}</TableCell>
                            <TableCell><Badge variant={device.evaluation.finalGrade === 'جاهز للبيع' ? 'default' : device.evaluation.finalGrade === 'تالف' || device.evaluation.finalGrade === 'عيب فني' ? 'destructive' : 'secondary'}>{device.evaluation.finalGrade}</Badge></TableCell>
                            <TableCell>{device.evaluation.externalGrade}</TableCell>
                            <TableCell>{device.evaluation.screenGrade}</TableCell>
                            <TableCell>{device.evaluation.networkGrade}</TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
            </ScrollArea>
            <DialogFooter>
                <DialogClose asChild>
                    <Button variant="outline">إغلاق</Button>
                </DialogClose>
            </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
}