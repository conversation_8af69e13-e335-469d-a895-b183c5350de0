import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const connectionId = searchParams.get('connectionId');

    const where = connectionId ? { connectionId: parseInt(connectionId) } : {};

    const backups = await prisma.databaseBackup.findMany({
      where,
      include: {
        connection: {
          select: {
            name: true,
            database: true,
            host: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      backups
    });
  } catch (error) {
    console.error('Failed to fetch backups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch database backups' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { connectionId, name, description } = await request.json();

    if (!connectionId) {
      return NextResponse.json(
        { error: 'Connection ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات الاتصال
      const connection = await tx.databaseConnection.findUnique({
        where: { id: connectionId }
      });

      if (!connection) {
        throw new Error('Database connection not found');
      }

      // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
      const backupDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // إنشاء اسم الملف
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // مطلوب لاسم الملف
      const fileName = `${connection.database}_${timestamp}.sql`;
      const filePath = path.join(backupDir, fileName);

      // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات أولاً
      const backup = await tx.databaseBackup.create({
        data: {
          name: name || `نسخة احتياطية - ${new Date()}`,
          description: description || '',
          filePath,
          fileSize: '0 MB', // سيتم تحديثه بعد إنشاء الملف
          backupType: 'manual',
          status: 'pending',
          connectionId,
          createdBy: authResult.user!.username,
        },
        include: {
          connection: {
            select: {
              name: true,
              database: true,
            }
          }
        }
      });

      try {
        // فك تشفير كلمة المرور للاستخدام في الأمر
        const dbPassword = connection.password;

        // تنفيذ أمر pg_dump
        const dumpCommand = `pg_dump -h ${connection.host} -p ${connection.port} -U ${connection.username} -d ${connection.database} -f "${filePath}" --no-password`;

        // تعيين متغير البيئة لكلمة المرور
        const env = {
          ...process.env,
          PGPASSWORD: dbPassword
        };

        await execAsync(dumpCommand, { env });

        // الحصول على حجم الملف
        const stats = fs.statSync(filePath);
        const fileSize = `${(stats.size / 1024 / 1024).toFixed(2)} MB`;

        // تحديث معلومات النسخة الاحتياطية
        const updatedBackup = await tx.databaseBackup.update({
          where: { id: backup.id },
          data: {
            fileSize,
            status: 'completed'
          },
          include: {
            connection: {
              select: {
                name: true,
                database: true,
              }
            }
          }
        });

        // إنشاء audit log
        await createAuditLogInTransaction(tx, {
          userId: authResult.user!.id,
          username: authResult.user!.username,
          operation: 'CREATE',
          details: `Created database backup: ${updatedBackup.name} for ${connection.name}`,
          tableName: 'databaseBackup',
          recordId: updatedBackup.id.toString()
        });

        return updatedBackup;
      } catch (backupError) {
        // تحديث حالة النسخة الاحتياطية إلى فاشلة
        await tx.databaseBackup.update({
          where: { id: backup.id },
          data: {
            status: 'failed'
          }
        });

        throw new Error('Failed to create database backup: ' + (backupError as Error).message);
      }
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Backup creation error:', error);

    if (error instanceof Error) {
      if (error.message.includes('Database connection not found')) {
        return NextResponse.json({ error: 'Database connection not found' }, { status: 404 });
      }
      if (error.message.includes('Failed to create database backup')) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }
    }

    return NextResponse.json(
      { error: 'Failed to create database backup' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Backup ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // الحصول على معلومات النسخة الاحتياطية
      const backup = await tx.databaseBackup.findUnique({
        where: { id },
        include: {
          connection: {
            select: { name: true }
          }
        }
      });

      if (!backup) {
        throw new Error('Database backup not found');
      }

      // حذف الملف من النظام إذا كان موجوداً
      if (fs.existsSync(backup.filePath)) {
        fs.unlinkSync(backup.filePath);
      }

      // حذف النسخة الاحتياطية من قاعدة البيانات
      await tx.databaseBackup.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted database backup: ${backup.name}`,
        tableName: 'databaseBackup',
        recordId: id.toString()
      });

      return { message: 'Database backup deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete backup:', error);

    if (error instanceof Error && error.message === 'Database backup not found') {
      return NextResponse.json({ error: 'Database backup not found' }, { status: 404 });
    }

    return NextResponse.json(
      { error: 'Failed to delete database backup' },
      { status: 500 }
    );
  }
}
