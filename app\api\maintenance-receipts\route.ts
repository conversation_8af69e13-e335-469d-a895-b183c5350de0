import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع إيصالات الصيانة مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { id: 'asc' as const }
      : { id: 'desc' as const };

    const maintenanceReceipts = await prisma.maintenanceReceiptOrder.findMany({
      orderBy
    });

    return NextResponse.json(maintenanceReceipts);
  } catch (error) {
    console.error('Failed to fetch maintenance receipts:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance receipts' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newReceipt = await request.json();

    // Basic validation
    if (!newReceipt.employeeName && !authResult.user!.username) {
      return NextResponse.json(
        { error: 'Employee name is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // إنشاء رقم تسلسلي بنمط REC-
      let receiptNumber = newReceipt.receiptNumber;

      if (!receiptNumber) {
        const existingReceipts = await tx.maintenanceReceiptOrder.findMany({
          select: { receiptNumber: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;
        existingReceipts.forEach(receipt => {
          const match = receipt.receiptNumber.match(/REC-(\d+)$/);
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        receiptNumber = `REC-${maxNumber + 1}`;
      } else {
        // التحقق من وجود الرقم المرسل
        const existingReceipt = await tx.maintenanceReceiptOrder.findUnique({
          where: { receiptNumber }
        });

        if (existingReceipt) {
          // إذا كان الرقم موجوداً، نولد رقماً جديداً
          const existingReceipts = await tx.maintenanceReceiptOrder.findMany({
            select: { receiptNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxNumber = 0;
          existingReceipts.forEach(receipt => {
            const match = receipt.receiptNumber.match(/REC-(\d+)$/);
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          });

          receiptNumber = `REC-${maxNumber + 1}`;
        }
      }

      // Create the maintenance receipt in the database
      const receipt = await tx.maintenanceReceiptOrder.create({
        data: {
          receiptNumber,
          referenceNumber: newReceipt.referenceNumber || null,
          date: newReceipt.date || new Date(),
          employeeName: newReceipt.employeeName || authResult.user!.username,
          maintenanceEmployeeName: newReceipt.maintenanceEmployeeName || null,
          items: newReceipt.items ? JSON.stringify(newReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),
          notes: newReceipt.notes || null,
          status: newReceipt.status || 'completed',
          attachmentName: newReceipt.attachmentName || null,
        }
      });

      // Update device statuses if items are provided
      if (newReceipt.items && Array.isArray(newReceipt.items)) {
        for (const item of newReceipt.items) {
          if (item.deviceId || item.id) {
            const deviceId = item.deviceId || item.id;
            const device = await tx.device.findUnique({
              where: { id: deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: deviceId },
                data: { status: 'متاح للبيع' }
              });
            } else {
              console.warn(`Device ${deviceId} not found for maintenance receipt ${receipt.receiptNumber}`);
            }
          }
        }
      }

      // ملاحظة: items يتم حفظها كـ JSON string في حقل items

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created maintenance receipt: ${receipt.receiptNumber}`,
        tableName: 'maintenanceReceiptOrder',
        recordId: receipt.id.toString()
      });

      return receipt;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create maintenance receipt:', error);

    if (error instanceof Error && error.message === 'Maintenance receipt with this number already exists') {
      return NextResponse.json({ error: 'Maintenance receipt with this number already exists' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create maintenance receipt' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedReceipt = await request.json();

    if (!updatedReceipt.id) {
      return NextResponse.json(
        { error: 'Maintenance receipt ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance receipt exists
      const existingReceipt = await tx.maintenanceReceiptOrder.findUnique({
        where: { id: updatedReceipt.id }
      });

      if (!existingReceipt) {
        throw new Error('Maintenance receipt not found');
      }

      // Update the maintenance receipt
      const receipt = await tx.maintenanceReceiptOrder.update({
        where: { id: updatedReceipt.id },
        data: {
          receiptNumber: updatedReceipt.receiptNumber,
          referenceNumber: updatedReceipt.referenceNumber,
          date: updatedReceipt.date,
          employeeName: updatedReceipt.employeeName,
          maintenanceEmployeeName: updatedReceipt.maintenanceEmployeeName,
          items: updatedReceipt.items ? JSON.stringify(updatedReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),
          notes: updatedReceipt.notes,
          status: updatedReceipt.status,
          attachmentName: updatedReceipt.attachmentName,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated maintenance receipt: ${receipt.receiptNumber}`,
        tableName: 'maintenanceReceiptOrder',
        recordId: receipt.id.toString()
      });

      return receipt;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update maintenance receipt:', error);

    if (error instanceof Error && error.message === 'Maintenance receipt not found') {
      return NextResponse.json({ error: 'Maintenance receipt not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update maintenance receipt' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Maintenance receipt ID is required' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance receipt exists
      const existingReceipt = await tx.maintenanceReceiptOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingReceipt) {
        throw new Error('Maintenance receipt not found');
      }

      // Parse items to update device statuses back
      let items = [];
      try {
        items = typeof existingReceipt.items === 'string' ?
          existingReceipt.items : existingReceipt.items;
      } catch (error) {
        console.warn('Failed to parse items for device status update:', error);
      }

      // Update device statuses back to maintenance
      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.deviceId || item.id) {
            const deviceId = item.deviceId || item.id;
            const device = await tx.device.findUnique({
              where: { id: deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: deviceId },
                data: { status: 'قيد الإصلاح' }
              });
            }
          }
        }
      }

      // Delete the maintenance receipt
      await tx.maintenanceReceiptOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted maintenance receipt: ${existingReceipt.receiptNumber}`,
        tableName: 'maintenanceReceiptOrder',
        recordId: id.toString()
      });

      return { message: 'Maintenance receipt deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete maintenance receipt:', error);

    if (error instanceof Error && error.message === 'Maintenance receipt not found') {
      return NextResponse.json({ error: 'Maintenance receipt not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete maintenance receipt' }, { status: 500 });
  }
}
