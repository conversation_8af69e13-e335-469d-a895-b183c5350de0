import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// دالة فحص الطلبات المتأخرة
async function checkOverdueRequests(): Promise<boolean> {
  try {
    const now = new Date();

    // جلب الطلبات المعلقة
    const pendingRequests = await prisma.employeeRequest.findMany({
      where: {
        status: {
          in: ['قيد المراجعة', 'قيد المراجعة المتقدمة']
        }
      }
    });

    for (const request of pendingRequests) {
      const requestDate = new Date(request.requestDate);
      const minutesSinceRequest = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60));

      // تحديد الحد الزمني حسب الأولوية
      let timeThreshold = 24 * 60; // 24 ساعة للطلبات العادية
      if (request.priority === 'طاريء') {
        timeThreshold = 4 * 60; // 4 ساعات
      } else if (request.priority === 'طاريء جدا') {
        timeThreshold = 60; // ساعة واحدة
      }

      // إذا تجاوز الطلب الحد الزمني
      if (minutesSinceRequest >= timeThreshold) {
        // التحقق من عدم إرسال إشعار مؤخراً
        const recentNotification = await prisma.notification.findFirst({
          where: {
            requestId: request.id,
            type: 'overdue',
            createdAt: {
              gte: new Date(now.getTime() - (timeThreshold * 60 * 1000 / 2))
            }
          }
        });

        if (!recentNotification) {
          // إرسال إشعار تأخير
          const managers = await prisma.user.findMany({
            where: { role: { in: ['manager', 'admin'] } }
          });

          const notifications = managers.map(manager => ({
            userId: manager.id,
            type: 'overdue',
            title: 'تذكير: طلب معلق يحتاج مراجعة',
            message: `الطلب ${request.requestNumber} من ${request.employeeName} معلق منذ أكثر من ${getOverdueTimeText(request.priority)}. يرجى المراجعة.`,
            requestId: request.id,
            requestNumber: request.requestNumber,
            employeeName: request.employeeName,
            priority: request.priority,
            actionRequired: true,
            read: false
          }));

          await prisma.notification.createMany({
            data: notifications
          });
        }
      }
    }

    return true;
  } catch (error) {
    console.error('خطأ في فحص الطلبات المتأخرة:', error);
    return false;
  }
}

// دالة مساعدة لتحديد نص وقت التأخير
function getOverdueTimeText(priority: string): string // ليس متعلق بالتواريخ {
  switch (priority) {
    case 'طاريء جدا':
      return 'ساعة واحدة';
    case 'طاريء':
      return '4 ساعات';
    default:
      return '24 ساعة';
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من المفتاح السري للحماية (اختياري)
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // تشغيل فحص الطلبات المتأخرة
    const result = await checkOverdueRequests();
    
    if (result) {
      return NextResponse.json({ 
        success: true, 
        message: 'Overdue requests check completed successfully' 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'Failed to check overdue requests' 
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in overdue requests check:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

// يمكن استدعاؤها أيضاً عبر GET للاختبار
export async function GET(request: NextRequest) {
  try {
    const result = await checkOverdueRequests();
    
    return NextResponse.json({ 
      success: result,
      message: result ? 'Check completed' : 'Check failed',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error in overdue requests check:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
